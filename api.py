import os
from flask import request, jsonify, send_from_directory
from urllib.parse import urlparse
from pathlib import Path

from codereview_app import api_app, port
from constants import SERVER_URL
from log import get_logger
from lib.handle_queue import handle_queue
from lib.webhook_handler import slugify_url
import json

from lib.worker import handle_push_event
from lib.model.ApiUsage import update_review_useful, get_review_record, ReviewRecord
from lib.reviewer_config import reviewer_config
from lib.scheduler import start_scheduler
from lib.notification.wechat import send_review_feedback_notification

@api_app.route('/media/<path:filename>')
def media(filename):
    project_root_path = Path(__file__).resolve().parent
    media_path = f'{project_root_path}/media'
    print(f'filename = {filename}, media_path = {media_path}')
    full_path = os.path.join(media_path, filename)
    # 访问 http://10.90.33.105:5010/media/index.html
    if not os.path.isfile(full_path):
        print(f"{full_path} File not found")
    return send_from_directory(media_path, filename)

@api_app.route('/review/webhook', methods=['POST'])
def handle_webhook():
    # 获取请求的JSON数据
    if request.is_json:
        data = request.get_json()
        if not data:
            return jsonify({"error": "Invalid JSON"}), 400
        print(f'request.endpoint = {request.endpoint}')
        return handle_gitlab_webhook(data, request.endpoint)
    else:
        return jsonify({'message': 'Invalid data format'}), 400


def handle_gitlab_webhook(data, endpoint):
    commit_list = data.get('commits', [])
    last_commit_id = commit_list[-1].get('id')
    logger = get_logger(last_commit_id)
    object_kind = data.get("object_kind")
    print(f'handle_gitlab_webhook endpoint = {endpoint}')

    # 优先从请求头获取，如果没有，则从环境变量获取，如果没有，则从推送事件中获取
    gitlab_url = os.getenv('GITLAB_URL') or request.headers.get('X-Gitlab-Instance')
    if not gitlab_url:
        repository = data.get('repository')
        if not repository:
            return jsonify({'message': 'Missing GitLab URL'}), 400
        homepage = repository.get("homepage")
        if not homepage:
            return jsonify({'message': 'Missing GitLab URL'}), 400
        try:
            parsed_url = urlparse(homepage)
            gitlab_url = f"{parsed_url.scheme}://{parsed_url.netloc}/"
        except Exception as e:
            return jsonify({"error": f"Failed to parse homepage URL: {str(e)}"}), 400

    # 优先从环境变量获取，如果没有，则从请求头获取
    gitlab_token = os.getenv('GITLAB_ACCESS_TOKEN') or request.headers.get('X-Gitlab-Token')
    # 如果gitlab_token为空，返回错误
    if not gitlab_token:
        return jsonify({'message': 'Missing GitLab access token'}), 400

    gitlab_url_slug = slugify_url(gitlab_url)

    project_root_path = Path(__file__).resolve().parent

    # 打印整个payload数据，或根据需求进行处理
    logger.info(f'Received event: {object_kind}')
    logger.info(f'Payload: {json.dumps(data)}')
    logger.info(f'gitlab_url：{gitlab_url}')
    logger.info(f'gitlab_url_slug：{gitlab_url_slug}')
    logger.info(f'project_root_path：{project_root_path}')

    if object_kind == "push":
        # 创建一个新进程进行异步处理
        handle_queue(handle_push_event, data, gitlab_token, gitlab_url, gitlab_url_slug, project_root_path, endpoint)
        # 立马返回响应
        return jsonify(
            {'message': f'Request received(object_kind={object_kind}), will process asynchronously.'}), 200
    else:
        error_message = f'Only push events are supported (both Webhook and System Hook), but received: {object_kind}.'
        return jsonify(error_message), 400

@api_app.route('/api/stats/review', methods=['GET'])
def get_review_stats():
    """获取代码审查统计数据"""
    try:
        with api_app.app_context():
            group_name = request.args.get('group')  # 新增组筛选参数
            
            # 如果没有指定组别，返回空数据
            if not group_name:
                return jsonify({
                    'success': True,
                    'total': 0,
                    'pending': 0,
                    'useful': 0,
                    'not_useful': 0,
                    'test': 0,
                    'group_name': None
                }), 200
            
            # 构建基础查询
            base_query = ReviewRecord.query
            
            # 按组筛选
            repositories = reviewer_config.get_repositories_by_group(group_name)
            if repositories:
                from sqlalchemy import or_
                base_query = base_query.filter(or_(*[ReviewRecord.project_name == repo for repo in repositories]))
            else:
                # 如果组不存在或没有仓库，返回空数据
                return jsonify({
                    'success': True,
                    'total': 0,
                    'pending': 0,
                    'useful': 0,
                    'not_useful': 0,
                    'test': 0,
                    'group_name': group_name
                }), 200
            
            # 总记录数
            total = base_query.count()
            
            # 待评价记录数（useful为None的记录）
            pending = base_query.filter(ReviewRecord.useful.is_(None)).count()
            
            # 有用记录数
            useful = base_query.filter(ReviewRecord.useful == 1).count()
            
            # 无用记录数
            not_useful = base_query.filter(ReviewRecord.useful == 0).count()
            
            # 测试代码记录数
            test_code = base_query.filter(ReviewRecord.useful == 2).count()
            
            return jsonify({
                'success': True,
                'total': total,
                'pending': pending,
                'useful': useful,
                'not_useful': not_useful,
                'test': test_code,
                'group_name': group_name
            }), 200
            
    except Exception as e:
        return jsonify({'error': f'Internal server error: {str(e)}'}), 500


@api_app.route('/api/review/pending', methods=['GET'])
def get_pending_reviews():
    """获取待评价的代码审查记录"""
    try:
        with api_app.app_context():
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 20, type=int)
            
            # 获取筛选参数
            user_name = request.args.get('user_name')
            project_name = request.args.get('project_name')
            reviewer_name = request.args.get('reviewer_name')  # 新增审核人筛选参数
            group_name = request.args.get('group')  # 新增组筛选参数
            
            # 如果没有指定组别，返回空数据
            if not group_name:
                return jsonify({
                    'success': True,
                    'records': [],
                    'total_pages': 0,
                    'current_page': page,
                    'total_records': 0,
                    'group_name': None
                }), 200
            
            # 构建查询
            query = ReviewRecord.query.filter(ReviewRecord.useful.is_(None))
            
            # 应用筛选条件
            if user_name:
                query = query.filter(ReviewRecord.user_name.ilike(f'%{user_name}%'))
            
            if project_name:
                query = query.filter(ReviewRecord.project_name.ilike(f'%{project_name}%'))
            
            # 按审核人筛选
            if reviewer_name:
                # 使用JSON字段查询审核人
                query = query.filter(ReviewRecord.reviewers.contains(f'"{reviewer_name}"'))
            
            # 按组筛选
            repositories = reviewer_config.get_repositories_by_group(group_name)
            if repositories:
                # 使用 OR 条件匹配组内的任何仓库
                from sqlalchemy import or_
                query = query.filter(or_(*[ReviewRecord.project_name == repo for repo in repositories]))
            else:
                # 如果组不存在或没有仓库，返回空数据
                return jsonify({
                    'success': True,
                    'records': [],
                    'total_pages': 0,
                    'current_page': page,
                    'total_records': 0,
                    'group_name': group_name
                }), 200
            
            # 按创建时间倒序排列
            query = query.order_by(ReviewRecord.created_at.desc())
            
            # 分页
            pagination = query.paginate(
                page=page, 
                per_page=per_page, 
                error_out=False
            )
            
            records = [record.to_dict() for record in pagination.items]
            
            return jsonify({
                'success': True,
                'records': records,
                'total_pages': pagination.pages,
                'current_page': page,
                'total_records': pagination.total,
                'group_name': group_name
            }), 200
            
    except Exception as e:
        return jsonify({'error': f'Internal server error: {str(e)}'}), 500


@api_app.route('/api/review/useful', methods=['POST'])
def mark_review_useful():
    """标记代码审查记录的有用性"""
    if not request.is_json:
        return jsonify({'error': 'Content-Type must be application/json'}), 400
    
    data = request.get_json()
    if not data:
        return jsonify({'error': 'Invalid JSON'}), 400
    
    commit_url = data.get('commit_url')
    useful = data.get('useful')
    
    if not commit_url:
        return jsonify({'error': 'Missing commit_url parameter'}), 400
    
    if useful not in [0, 1, 2]:
        return jsonify({'error': 'Invalid useful value. Must be 0, 1, or 2'}), 400
    
    try:
        success, record_dict = update_review_useful(commit_url, useful)
        
        if success and record_dict:
            # 发送微信通知给审核人员
            try:
                send_review_feedback_notification(
                    record=record_dict,
                    logger=get_logger('feedback_notification')
                )
            except Exception as e:
                # 通知发送失败不影响主流程
                print(f"发送审核完成通知失败: {str(e)}")

        if success:
            return jsonify({
                'success': True,
                'message': 'Review marked successfully',
                'useful': useful
            }), 200
        else:
            return jsonify({'error': 'Failed to update review record'}), 404
            
    except Exception as e:
        return jsonify({'error': f'Internal server error: {str(e)}'}), 500


@api_app.route('/api/groups', methods=['GET'])
def get_groups():
    """获取所有组配置"""
    try:
        groups = reviewer_config.get_groups()
        return jsonify({
            'success': True,
            'groups': groups
        }), 200
    except Exception as e:
        return jsonify({'error': f'Internal server error: {str(e)}'}), 500


@api_app.route('/api/groups/<group_name>/repositories', methods=['GET'])
def get_group_repositories(group_name):
    """获取指定组下的仓库列表"""
    try:
        repositories = reviewer_config.get_repositories_by_group(group_name)
        group_info = reviewer_config.get_group_by_name(group_name)
        
        if not group_info:
            return jsonify({'error': 'Group not found'}), 404
        
        return jsonify({
            'success': True,
            'group_name': group_name,
            'group_info': group_info,
            'repositories': repositories
        }), 200
    except Exception as e:
        return jsonify({'error': f'Internal server error: {str(e)}'}), 500


@api_app.route('/api/repositories/<repository_name>/group', methods=['GET'])
def get_repository_group(repository_name):
    """获取仓库所属的组"""
    try:
        group_name = reviewer_config.get_group_by_repository(repository_name)
        
        return jsonify({
            'success': True,
            'repository_name': repository_name,
            'group_name': group_name
        }), 200
    except Exception as e:
        return jsonify({'error': f'Internal server error: {str(e)}'}), 500


if __name__ == '__main__':
    # 在应用启动时启动定时任务
    start_scheduler()
    # 启动Flask API服务
    api_app.run(host='0.0.0.0', port=port)


# if __name__ == '__main__':
#     project_root_path = Path(__file__).resolve().parent
#     logger = get_logger('similar-log')
#     repo_url = 'https://code.37ops.com/37sy_android/37sdk_AS'
#     target_clone_dir = f'{project_root_path}/history/code'
#
#     clone_code(repo_url, target_clone_dir, 'test-ai-codereview', logger)
#     compare_with_history_code(target_clone_dir, "", logger, 'feat: 添加闪验环境判')
